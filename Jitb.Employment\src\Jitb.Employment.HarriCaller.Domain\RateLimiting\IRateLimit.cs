using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.RateLimiting
{
    /// <summary>
    /// Interface for rate limiting API calls
    /// </summary>
    public interface IRateLimit
    {        /// <summary>
        /// Checks if a request can be made based on the rate limit.
        /// NOTE: This method is NOT atomic and should be avoided in concurrent scenarios.
        /// Use TryMakeRequestAsync for atomic rate limiting.
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>True if the request can be made, false otherwise</returns>
        Task<bool> CanMakeRequestAsync(string key);

        /// <summary>
        /// Records that a request was made.
        /// NOTE: This method should only be used after CanMakeRequestAsync returns true.
        /// For atomic operations, use TryMakeRequestAsync instead.
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>Task representing the asynchronous operation</returns>
        Task RecordRequestAsync(string key);

        /// <summary>
        /// Atomically tries to make a request, checking the rate limit and recording it if allowed.
        /// This is the preferred method for rate limiting as it prevents race conditions.
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>True if the request was recorded and can be made, false otherwise</returns>
        Task<bool> TryMakeRequestAsync(string key);
    }
}
