using NLog;
using StackExchange.Redis;
using System;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.RateLimiting
{
    /// <summary>
    /// Fixed Redis-based implementation of rate limiting using a sliding window algorithm
    /// with high precision timestamps to prevent overwriting
    /// </summary>
    public class RateLimitRedisFixed : IRateLimit
    {
        private readonly IConnectionMultiplexer _redis;
        private readonly ILogger _log;
        private readonly int _maxRequestsPerMinute;
        private readonly string _keyPrefix;

        /// <summary>
        /// Initializes a new instance of the <see cref="RateLimitRedisFixed"/> class.
        /// </summary>
        /// <param name="connectionString">Redis connection string</param>
        /// <param name="log">Logger instance</param>
        /// <param name="maxRequestsPerMinute">Maximum number of requests allowed per minute (default: 200)</param>
        /// <param name="keyPrefix">Prefix for Redis keys to avoid collisions (default: "harri-rate-limit:")</param>
        public RateLimitRedisFixed(string connectionString, ILogger log, int maxRequestsPerMinute = 200, string keyPrefix = "harri-rate-limit:")
        {
            // Configure connection options with proper timeouts
            var options = ConfigurationOptions.Parse(connectionString);
            options.ConnectTimeout = 5000;    // 5 second connect timeout
            options.SyncTimeout = 5000;       // 5 second sync timeout
            options.AsyncTimeout = 5000;      // 5 second async timeout
            options.AbortOnConnectFail = false; // Don't abort immediately on connection failure
            
            _redis = ConnectionMultiplexer.Connect(options);
            _log = log;
            _maxRequestsPerMinute = maxRequestsPerMinute;
            _keyPrefix = keyPrefix;
        }

        /// <summary>
        /// Checks if a request can be made based on the rate limit
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>True if the request can be made, false otherwise</returns>
        public async Task<bool> CanMakeRequestAsync(string key)
        {
            var db = _redis.GetDatabase();
            var redisKey = $"{_keyPrefix}{key}";

            // Get the current timestamp with high precision (ticks to ensure uniqueness)
            var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();

            // Remove timestamps older than 1 minute (60,000 milliseconds)
            var cutoff = now - 60000;
            await db.SortedSetRemoveRangeByScoreAsync(redisKey, 0, cutoff);

            // Count the number of requests in the last minute
            var count = await db.SortedSetLengthAsync(redisKey);

            _log.Debug($"Rate limit check for {key}: {count}/{_maxRequestsPerMinute} requests in the last minute");

            return count < _maxRequestsPerMinute;
        }

        /// <summary>
        /// Records that a request was made
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task RecordRequestAsync(string key)
        {
            var db = _redis.GetDatabase();
            var redisKey = $"{_keyPrefix}{key}";

            // Get the current timestamp with high precision (milliseconds + counter for uniqueness)
            var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            
            // Create a unique score by combining timestamp with a small random component
            // This ensures that even simultaneous requests get unique scores
            var random = new Random();
            var uniqueScore = now + (random.NextDouble() / 1000000); // Add microsecond precision
            
            // Create a unique member name to prevent overwrites
            var uniqueMember = $"{now}_{Guid.NewGuid().ToString("N")[0..8]}";

            // Add the current timestamp to the sorted set with unique member
            await db.SortedSetAddAsync(redisKey, uniqueMember, uniqueScore);

            // Set expiration for the key (2 minutes to ensure cleanup)
            await db.KeyExpireAsync(redisKey, TimeSpan.FromMinutes(2));

            _log.Debug($"Recorded request for {key} at {uniqueScore} with member {uniqueMember}");
        }

        /// <summary>
        /// Tries to make a request, recording it if possible (atomic operation)
        /// </summary>
        /// <param name="key">The key to identify the rate limit group (e.g., API endpoint)</param>
        /// <returns>True if the request was recorded and can be made, false otherwise</returns>
        public async Task<bool> TryMakeRequestAsync(string key)
        {
            var db = _redis.GetDatabase();
            var redisKey = $"{_keyPrefix}{key}";

            // Use a Lua script for atomic rate limiting check and record
            const string luaScript = @"
                local key = KEYS[1]
                local max_requests = tonumber(ARGV[1])
                local current_time = tonumber(ARGV[2])
                local cutoff_time = tonumber(ARGV[3])
                local unique_member = ARGV[4]
                local unique_score = tonumber(ARGV[5])
                
                -- Remove old entries
                redis.call('ZREMRANGEBYSCORE', key, 0, cutoff_time)
                
                -- Check current count
                local current_count = redis.call('ZCARD', key)
                
                if current_count < max_requests then
                    -- Add the new request
                    redis.call('ZADD', key, unique_score, unique_member)
                    -- Set expiration
                    redis.call('EXPIRE', key, 120)
                    return 1
                else
                    return 0
                end
            ";

            var now = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds();
            var cutoff = now - 60000; // 60 seconds ago in milliseconds
            var random = new Random();
            var uniqueScore = now + (random.NextDouble() / 1000000);
            var uniqueMember = $"{now}_{Guid.NewGuid().ToString("N")[0..8]}";

            try
            {
                var result = await db.ScriptEvaluateAsync(luaScript, 
                    new RedisKey[] { redisKey }, 
                    new RedisValue[] { _maxRequestsPerMinute, now, cutoff, uniqueMember, uniqueScore });

                var canMakeRequest = (int)result == 1;
                
                if (canMakeRequest)
                {
                    _log.Debug($"Rate limit: Request allowed for {key} at {uniqueScore}");
                }
                else
                {
                    _log.Warn($"Rate limit exceeded for {key}. Request rejected.");
                }

                return canMakeRequest;
            }
            catch (Exception ex)
            {
                _log.Error($"Error in TryMakeRequestAsync for {key}: {ex.Message}");
                _log.Error(ex);
                
                // Fall back to non-atomic approach if Lua script fails
                if (await CanMakeRequestAsync(key))
                {
                    await RecordRequestAsync(key);
                    return true;
                }
                return false;
            }
        }

        /// <summary>
        /// Dispose of the Redis connection
        /// </summary>
        public void Dispose()
        {
            _redis?.Dispose();
        }
    }
}
