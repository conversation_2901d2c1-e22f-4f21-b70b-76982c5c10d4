﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1A286B7F-19CA-4B84-A204-8DF96F4E9160}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Jitb.Employment.HarriCaller.Domain</RootNamespace>
    <AssemblyName>Jitb.Employment.HarriCaller.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <!-- <LangVersion>latest</LangVersion> -->
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Afterman.nRepo, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Afterman.nRepo.2024.5.28.3\lib\net461\Afterman.nRepo.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime, Version=3.5.0.2, Culture=neutral, PublicKeyToken=eb42632606e9261f, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Antlr3.Runtime.3.5.1\lib\net40-client\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="Ardalis.GuardClauses, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Ardalis.GuardClauses.5.0.0\lib\netstandard2.0\Ardalis.GuardClauses.dll</HintPath>
    </Reference>
    <Reference Include="FluentNHibernate, Version=2.0.3.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\FluentNHibernate.2.0.3.0\lib\net40\FluentNHibernate.dll</HintPath>
    </Reference>
    <Reference Include="Iesi.Collections, Version=4.0.0.4000, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Iesi.Collections.4.0.4\lib\net461\Iesi.Collections.dll</HintPath>
    </Reference>
    <Reference Include="JetBrains.Annotations, Version=2021.3.0.0, Culture=neutral, PublicKeyToken=1010a0d8d6380325, processorArchitecture=MSIL">
      <HintPath>..\..\packages\JetBrains.Annotations.2021.3.0\lib\net20\JetBrains.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="Jitb.CommonLibrary, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\packages\Jitb.CommonLibrary.2024.10.17.2\lib\net48\Jitb.CommonLibrary.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.DependencyInjection.6.0.0\lib\net461\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\lib\net461\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NHibernate, Version=5.3.0.0, Culture=neutral, PublicKeyToken=aa95f207798dfdb4, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NHibernate.5.3.3\lib\net461\NHibernate.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NLog.4.7.10\lib\net45\NLog.dll</HintPath>
    </Reference>    <Reference Include="NServiceBus.Core, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.7.8.5\lib\net452\NServiceBus.Core.dll</HintPath>
    </Reference>
    <Reference Include="NServiceBus.NLog, Version=*******, Culture=neutral, PublicKeyToken=9fc386479f8a226c, processorArchitecture=MSIL">
      <HintPath>..\..\packages\NServiceBus.NLog.3.0.0\lib\net452\NServiceBus.NLog.dll</HintPath>
    </Reference>
    <Reference Include="Pipelines.Sockets.Unofficial, Version=*******, Culture=neutral, PublicKeyToken=42ea0a778e13fbe2, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Pipelines.Sockets.Unofficial.2.2.8\lib\net472\Pipelines.Sockets.Unofficial.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq, Version=*******, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Remotion.Linq.2.2.0\lib\net45\Remotion.Linq.dll</HintPath>
    </Reference>
    <Reference Include="Remotion.Linq.EagerFetching, Version=*******, Culture=neutral, PublicKeyToken=fee00910d6e5f53b, processorArchitecture=MSIL">
      <HintPath>..\..\packages\Remotion.Linq.EagerFetching.2.2.0\lib\net45\Remotion.Linq.EagerFetching.dll</HintPath>
    </Reference>
    <Reference Include="RestSharp, Version=*********, Culture=neutral, PublicKeyToken=598062e77f915f75, processorArchitecture=MSIL">
      <HintPath>..\..\packages\RestSharp.110.2.0\lib\net471\RestSharp.dll</HintPath>
    </Reference>
    <Reference Include="StackExchange.Redis, Version=********, Culture=neutral, PublicKeyToken=c219ff1ca8c2ce46, processorArchitecture=MSIL">
      <HintPath>..\..\packages\StackExchange.Redis.2.8.37\lib\net472\StackExchange.Redis.dll</HintPath>
    </Reference>
    <Reference Include="StructureMap, Version=4.7.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\StructureMap.4.7.1\lib\net45\StructureMap.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Diagnostics.DiagnosticSource.6.0.0\lib\net461\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Compression, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.IO.Pipelines.5.0.1\lib\net461\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.AccessControl.6.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Xml, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.Cryptography.Xml.6.0.1\lib\net461\System.Security.Cryptography.Xml.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Text.Encodings.Web.8.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Text.Json.8.0.3\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Threading.Channels.5.0.0\lib\net461\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="ValueOf, Version=*******, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\..\packages\ValueOf.2.0.21\lib\net451\ValueOf.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Concepts\HarriEmployeeAbsence.cs" />
    <Compile Include="Concepts\HarriEmployeeMappings.cs" />
    <Compile Include="Concepts\HarriInboundEmployeeData.cs" />
    <Compile Include="Constants\HarriCrossLocationModes.cs" />
    <Compile Include="Constants\HarriStatusCodes.cs" />
    <Compile Include="HarriPayloads\HarriEndSharePayload.cs" />
    <Compile Include="HarriPayloads\HarriStartLoaPayload.cs" />
    <Compile Include="HarriPayloads\Pagination.cs" />
    <Compile Include="Position.cs" />
    <Compile Include="Providers\CallHarriWebServiceProvider.cs" />
    <Compile Include="Concepts\HarriEmploymentPeriod.cs" />
    <Compile Include="Concepts\HarriEvents.cs" />
    <Compile Include="Concepts\HarriInboundEmployee.cs" />
    <Compile Include="Concepts\HarriLocation.cs" />
    <Compile Include="Concepts\HarriLocations.cs" />
    <Compile Include="Concepts\HarriMapping.cs" />
    <Compile Include="Concepts\HarriMappingData.cs" />
    <Compile Include="Concepts\HarriMappings.cs" />
    <Compile Include="Concepts\HarriPosition.cs" />
    <Compile Include="Concepts\HarriTerminationReason.cs" />
    <Compile Include="Concepts\UnmappedEmployee.cs" />
    <Compile Include="Constants\HarriPayTypes.cs" />
    <Compile Include="Constants\JsonSerializer.cs" />
    <Compile Include="DomainRegistry.cs" />
    <Compile Include="Enums\CallTypes.cs" />
    <Compile Include="Enums\TerminationReasons.cs" />
    <Compile Include="Enums\UrlType.cs" />
    <Compile Include="Exceptions\InvalidCallToHarriException.cs" />
    <Compile Include="Exceptions\TenantNotFoundForLocationException.cs" />
    <Compile Include="HarriPayloads\Address.cs" />
    <Compile Include="HarriPayloads\HarriNewPayTypePayload.cs" />
    <Compile Include="HarriPayloads\HarriAttachLocationPayload.cs" />
    <Compile Include="HarriPayloads\HarriChangeAnnualPayRatePayload.cs" />
    <Compile Include="HarriPayloads\HarriChangeHourlyPayRatePayload.cs" />
    <Compile Include="HarriPayloads\HarriChangePositionPayload.cs" />
    <Compile Include="HarriPayloads\HarriNewHirePayload.cs" />
    <Compile Include="HarriPayloads\HarriPositionPayload.cs" />
    <Compile Include="HarriPayloads\HarriMappingPayload.cs" />
    <Compile Include="HarriPayloads\HarriPostMappingsWrapperPayload.cs" />
    <Compile Include="HarriPayloads\HarriRehirePayload.cs" />
    <Compile Include="HarriPayloads\HarriSchedulePayload.cs" />
    <Compile Include="HarriPayloads\HarriTerminationPayload.cs" />
    <Compile Include="HarriPayloads\HarriUpdateEmployeePayload.cs" />
    <Compile Include="HarriPayloads\PayRate.cs" />
    <Compile Include="HarriPayloads\PayType.cs" />
    <Compile Include="Constants\PayloadMappingEndpoints.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Providers\CallHarriWebserviceProviderExtended.cs" />
    <Compile Include="Providers\GetHarriEmployeeProvider.cs" />
    <Compile Include="Providers\HarriUrlGenerator.cs" />
    <Compile Include="Providers\HarriUrlGeneratorFactory.cs" />
    <Compile Include="Providers\IHarriUrlGeneratorOld.cs" />
    <Compile Include="RateLimiting\IRateLimit.cs" />
    <Compile Include="RateLimiting\RateLimitMemory.cs" />
    <Compile Include="RateLimiting\RateLimitRedis.cs" />
    <Compile Include="RateLimiting\RateLimitRedisFixed.cs" />
    <Compile Include="Providers\TenantByLocationProvider.cs" />
    <Compile Include="SerializerSettings\DateFormatConverter.cs" />
    <Compile Include="TokenResponse.cs" />
    <Compile Include="ValueObjects\Address.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ApiServices\" />
    <Folder Include="Helpers\" />
    <Folder Include="Repositories\Maps\" />
  </ItemGroup>
  <ItemGroup>
    <Analyzer Include="..\..\packages\NServiceBus.7.8.5\analyzers\dotnet\cs\NServiceBus.Core.Analyzer.dll" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Jitb.Employment.Domain\Jitb.Employment.Domain.csproj">
      <Project>{3955df0a-7228-4f87-9810-09659e9561b0}</Project>
      <Name>Jitb.Employment.Domain</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('..\..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\..\packages\Microsoft.Extensions.Logging.Abstractions.6.0.0\build\Microsoft.Extensions.Logging.Abstractions.targets'))" />
  </Target>
</Project>