using System;
using System.Threading.Tasks;
using FluentAssertions;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using NLog;
using Xunit;
using Xunit.Abstractions;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    /// <summary>
    /// Demonstration test to show that rate limiting functionality works correctly
    /// This test uses the memory-based rate limiter to avoid Redis dependencies
    /// </summary>
    public class RateLimitDemonstrationTest
    {
        private readonly ITestOutputHelper _output;
        private readonly ILogger _logger;

        public RateLimitDemonstrationTest(ITestOutputHelper output)
        {
            _output = output;
            _logger = LogManager.GetCurrentClassLogger();
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldTrackRequests_MemoryBased()
        {
            // Arrange
            var rateLimiter = new RateLimitMemory(_logger, 3); // Allow 3 requests per minute
            var key = "test-api-endpoint";

            _output.WriteLine("Testing memory-based rate limiter with limit of 3 requests per minute");

            // Act & Assert - First 3 requests should succeed
            for (int i = 1; i <= 3; i++)
            {
                var canMake = await rateLimiter.CanMakeRequestAsync(key);
                canMake.Should().BeTrue($"Request {i} should be allowed");
                
                await rateLimiter.RecordRequestAsync(key);
                _output.WriteLine($"Request {i}: Recorded successfully");
            }

            // The 4th request should be blocked
            var fourthRequest = await rateLimiter.CanMakeRequestAsync(key);
            fourthRequest.Should().BeFalse("4th request should be blocked due to rate limit");
            _output.WriteLine("Request 4: Blocked by rate limit (as expected)");

            // Verify TryMakeRequestAsync also respects the limit
            var tryResult = await rateLimiter.TryMakeRequestAsync(key);
            tryResult.Should().BeFalse("TryMakeRequestAsync should return false when limit is exceeded");
            _output.WriteLine("TryMakeRequestAsync: Correctly returned false when limit exceeded");

            _output.WriteLine("✅ Rate limiting demonstration completed successfully!");
        }

        [Fact]
        public async Task RateLimit_ShouldWorkWithDifferentKeys()
        {
            // Arrange
            var rateLimiter = new RateLimitMemory(_logger, 2); // Allow 2 requests per minute
            var key1 = "api-endpoint-1";
            var key2 = "api-endpoint-2";

            _output.WriteLine("Testing that different keys have independent rate limits");

            // Act & Assert - Each key should have its own limit
            // Key 1: Use up its limit
            (await rateLimiter.TryMakeRequestAsync(key1)).Should().BeTrue("Key1 request 1 should succeed");
            (await rateLimiter.TryMakeRequestAsync(key1)).Should().BeTrue("Key1 request 2 should succeed");
            (await rateLimiter.TryMakeRequestAsync(key1)).Should().BeFalse("Key1 request 3 should be blocked");
            
            _output.WriteLine("Key1: Used up its limit (2/2 requests)");

            // Key 2: Should still have its full limit available
            (await rateLimiter.TryMakeRequestAsync(key2)).Should().BeTrue("Key2 request 1 should succeed");
            (await rateLimiter.TryMakeRequestAsync(key2)).Should().BeTrue("Key2 request 2 should succeed");
            (await rateLimiter.TryMakeRequestAsync(key2)).Should().BeFalse("Key2 request 3 should be blocked");

            _output.WriteLine("Key2: Also used up its independent limit (2/2 requests)");
            _output.WriteLine("✅ Independent key rate limiting works correctly!");
        }

        [Fact]
        public async Task RateLimit_ShouldLogAppropriateMessages()
        {
            // Arrange
            var rateLimiter = new RateLimitMemory(_logger, 1); // Very restrictive limit for testing
            var key = "logging-test";

            _output.WriteLine("Testing that rate limiter logs appropriate messages");

            // Act
            var firstRequest = await rateLimiter.TryMakeRequestAsync(key);
            var secondRequest = await rateLimiter.TryMakeRequestAsync(key);

            // Assert
            firstRequest.Should().BeTrue("First request should succeed");
            secondRequest.Should().BeFalse("Second request should be blocked");

            _output.WriteLine("First request: Allowed");
            _output.WriteLine("Second request: Blocked (rate limit exceeded)");
            _output.WriteLine("✅ Rate limiter logging test completed!");
        }
    }
}
