using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using Moq;
using NLog;
using System;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    public class RateLimitMemoryIntegrationTests
    {
        [Fact(Skip = "This is an integration test.  It must run on its own.  It may cause other tests to be unreliable")]
        public async Task TryMakeRequestAsync_Should_Enforce_Limit_And_Reset_After_Window()
        {
            // Arrange
            int maxRequests = 5;
            var logger = new Mock<ILogger>();
            var rateLimiter = new RateLimitMemory(logger.Object, maxRequests);
            string key = "test-key";
            int totalAttempts = 20;
            int accepted = 0, rejected = 0;

            // Act: Simulate concurrent requests
            var tasks = new Task[totalAttempts];
            for (int i = 0; i < totalAttempts; i++)
            {
                tasks[i] = Task.Run(async () =>
                {
                    if (await rateLimiter.TryMakeRequestAsync(key))
                        Interlocked.Increment(ref accepted);
                    else
                        Interlocked.Increment(ref rejected);
                });
            }
            await Task.WhenAll(tasks);

            // Assert: Only maxRequests should be accepted
            Assert.Equal(maxRequests, accepted);
            Assert.Equal(totalAttempts - maxRequests, rejected);

            // Wait for the window to expire
            await Task.Delay(TimeSpan.FromSeconds(61));
            accepted = 0;
            rejected = 0;

            // Act: Try again after window
            tasks = new Task[totalAttempts];
            for (int i = 0; i < totalAttempts; i++)
            {
                tasks[i] = Task.Run(async () =>
                {
                    if (await rateLimiter.TryMakeRequestAsync(key))
                        Interlocked.Increment(ref accepted);
                    else
                        Interlocked.Increment(ref rejected);
                });
            }
            await Task.WhenAll(tasks);

            // Assert: Should again allow maxRequests
            Assert.Equal(maxRequests, accepted);
            Assert.Equal(totalAttempts - maxRequests, rejected);
        }
    }
}
