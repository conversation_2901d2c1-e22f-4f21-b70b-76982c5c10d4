﻿namespace Jitb.Employment.HarriCaller.Domain
{
    using Jitb.Employment.HarriCaller.Domain.Concepts;
    using Jitb.Employment.HarriCaller.Domain.Providers;
    using Jitb.Employment.HarriCaller.Domain.RateLimiting;
    using NLog;
    using StructureMap;
    using System.Configuration;

    public class DomainRegistry : Registry
    {
        public DomainRegistry()
        {
            // Register the rate limiter
            string redisConnectionString = ConfigurationManager.AppSettings["Redis:ConnectionString"] ?? "localhost:6379";
            int maxRequestsPerMinute = 200; // Default to 200 requests per minute
            bool useRedis = !string.IsNullOrEmpty(ConfigurationManager.AppSettings["Redis:UseRedis"]) &&
                           bool.TryParse(ConfigurationManager.AppSettings["Redis:UseRedis"], out bool useRedisValue) &&
                           useRedisValue;

            if (int.TryParse(ConfigurationManager.AppSettings["Harri:MaxRequestsPerMinute"], out int configuredRate))
            {
                maxRequestsPerMinute = configuredRate;
            }

            var logger = LogManager.GetCurrentClassLogger();

            if (useRedis)
            {
                logger.Info("Using Redis-based rate limiting");
                For<IRateLimit>().Use<RateLimitRedisFixed>()
                    .Ctor<string>("connectionString").Is(redisConnectionString)
                    .Ctor<ILogger>("log").Is(logger)
                    .Ctor<int>("maxRequestsPerMinute").Is(maxRequestsPerMinute)
                    .Ctor<string>("keyPrefix").Is("harri-rate-limit:");
            }
            else
            {
                logger.Info("Using memory-based rate limiting");
                For<IRateLimit>().Use<RateLimitMemory>()
                    .Ctor<ILogger>("log").Is(logger)
                    .Ctor<int>("maxRequestsPerMinute").Is(maxRequestsPerMinute);
            }

            For<ICallHarriWebServiceProvider>().Use<CallHarriWebServiceProvider>();
            For<ICallHarriWebserviceDeserialize<HarriLocations2List>>()
                .Use<CallHarriWebserviceDeserialize<HarriLocations2List>>();
            For<IGetHarriEmployeeProvider>().Use<GetHarriEmployeeProvider>();

            this.Scan(y =>
            {
                //for all interfaces like this:
                // public interface IFoo
                //tell structuremap to use the concrete class called
                // public class Foo : IFoo
                y.AssemblyContainingType<DomainRegistry>();
                y.WithDefaultConventions();
            });
        }
    }
}
