using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.HarriCaller.Domain;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.Providers;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    /// <summary>
    /// Enhanced CallHarriWebServiceProvider with timeout handling and debug logging for testing
    /// </summary>
    public class CallHarriWebServiceProviderDebug : ICallHarriWebServiceProvider
    {
        private readonly IHarriTenantTable _harriTenantTable;
        private readonly ILogger _log;
        private readonly IRateLimit _rateLimit;
        private const string BASEURL = @"https://gateway.harri.com/open-api-hub/";

        // Static dictionary to persist token cache across multiple calls
        private static readonly Dictionary<string, (string Token, DateTime ExpirationTime)> _tokenCache =
            new Dictionary<string, (string Token, DateTime ExpirationTime)>();
        private static readonly object _tokenCacheLock = new object();

        public CallHarriWebServiceProviderDebug(IHarriTenantTable harriTenantTable, ILogger log, IRateLimit rateLimit)
        {
            _harriTenantTable = harriTenantTable;
            _log = log;
            _rateLimit = rateLimit;
        }

        public async Task<RestResponse> Call(Guid tenantId, Method callType, string endpoint, string payload,
            bool failIfNotFound, string version)
        {
            _log.Info($@"
[DEBUG] Calling Harri With TenantId: {tenantId}, Method: {callType.ToString()}, Endpoint: {endpoint}, Payload: {payload}
");
            var tenant = _harriTenantTable.Get(tenantId);

            return await Call(tenant, callType, endpoint, payload, failIfNotFound, version);
        }

        public async Task<RestResponse> Call(HarriTenant tenant, Method callType, string endpoint, string payload,
            bool failIfNotFound, string version)
        {
            _log.Info($@"
[DEBUG] Calling Harri with Tenant: {tenant.TenantId}, Method: {callType.ToString()}, Endpoint: {endpoint}, Payload: {payload}, ClientId: {tenant.Client}
");
            string tokenUrl = tenant.TokenUrl;
            string clientId = tenant.Client;
            string clientSecret = tenant.Secret;

            var token = GetOAuthTokenAsync(tokenUrl, clientId, clientSecret, _log, tenant).GetAwaiter().GetResult();

            _log.Info($"[DEBUG] Token: {token}");

            var request = new RestRequest(endpoint, callType);
            return await CallHarri(tenant, token, request, payload, failIfNotFound, version);
        }

        private async Task<RestResponse> CallHarri(HarriTenant tenant, string pmToken, RestRequest request, string payload, bool failIfNotFound, string version)
        {
            var stopwatch = Stopwatch.StartNew();
            
            // replacing baseurl with constant instead of baseurl from tenant table.
            string url = $@"{BASEURL}/api/{version.ToLower()}";

            // Create a rate limit key based on the tenant and endpoint
            string rateLimitKey = $"harri-api-ratelimit";

            _log.Info($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Starting rate limit check for key: {rateLimitKey}");

            // Enhanced rate limiting with timeout and cancellation
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30)); // 30 second total timeout
            bool canMakeRequest = false;

            try
            {
                // Use TryMakeRequestAsync with timeout protection
                var rateLimitTask = _rateLimit.TryMakeRequestAsync(rateLimitKey);
                var timeoutTask = Task.Delay(10000, cts.Token); // 10 second timeout for individual call

                var completedTask = await Task.WhenAny(rateLimitTask, timeoutTask);

                if (completedTask == timeoutTask)
                {
                    _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate limit check TIMED OUT after 10 seconds for key: {rateLimitKey}");
                    throw new TimeoutException($"Rate limit check timed out after 10 seconds for key: {rateLimitKey}");
                }

                canMakeRequest = await rateLimitTask;
                _log.Info($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Initial rate limit check result: {canMakeRequest} for key: {rateLimitKey}");
            }
            catch (Exception ex)
            {
                _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate limit check failed: {ex.Message}");
                _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Stack trace: {ex.StackTrace}");
                throw;
            }

            int retryCount = 0;
            const int maxRetries = 30; // Maximum 30 retries (30 seconds)

            while (!canMakeRequest && retryCount < maxRetries && !cts.Token.IsCancellationRequested)
            {
                retryCount++;
                _log.Debug($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate Limit: {request.Resource} Too many requests - retry {retryCount}/{maxRetries}, delaying for 1 second");
                
                await Task.Delay(1000, cts.Token);
                
                try
                {
                    var retryRateLimitTask = _rateLimit.TryMakeRequestAsync(rateLimitKey);
                    var retryTimeoutTask = Task.Delay(5000, cts.Token); // 5 second timeout for retry attempts

                    var retryCompletedTask = await Task.WhenAny(retryRateLimitTask, retryTimeoutTask);

                    if (retryCompletedTask == retryTimeoutTask)
                    {
                        _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate limit retry {retryCount} TIMED OUT after 5 seconds");
                        throw new TimeoutException($"Rate limit retry {retryCount} timed out after 5 seconds");
                    }

                    canMakeRequest = await retryRateLimitTask;
                    _log.Debug($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate limit retry {retryCount} result: {canMakeRequest}");
                }
                catch (Exception ex)
                {
                    _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate limit retry {retryCount} failed: {ex.Message}");
                    throw;
                }
            }

            if (!canMakeRequest)
            {
                var errorMsg = cts.Token.IsCancellationRequested 
                    ? $"Rate limiting timed out after {stopwatch.ElapsedMilliseconds}ms (cancellation requested)"
                    : $"Rate limiting failed after {maxRetries} retries and {stopwatch.ElapsedMilliseconds}ms";
                
                _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] {errorMsg}");
                throw new TimeoutException(errorMsg);
            }

            _log.Debug($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Rate Limit: {request.Resource} Sending request after {retryCount} retries");

            using (var client = new RestClient(url))
            {
                var token = $@"Bearer {pmToken}";
                request.AddHeader("Authorization", token);
                request.Timeout = TimeSpan.FromMilliseconds(30000);
                if (payload != null) request.AddBody(payload);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                RestResponse resp;

                try
                {
                    _log.Info($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Executing HTTP request to: {url}{request.Resource}");
                    resp = await client.ExecuteAsync(request);
                    _log.Info($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Response Status: {resp.StatusCode}");
                }
                catch (Exception e)
                {
                    _log.Error($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] HTTP request failed: {e.Message}");
                    _log.Error(e);
                    throw;
                }

                if (resp.StatusCode != HttpStatusCode.OK && failIfNotFound)
                {
                    throw new InvalidCallToHarriException(resp, tenant, request, payload);
                }

                _log.Info($"[DEBUG] [{stopwatch.ElapsedMilliseconds}ms] Call completed successfully");
                return resp;
            }
        }

        private static async Task<string> GetOAuthTokenAsync(string tokenUrl, string clientId, string clientSecret, ILogger log, HarriTenant tenant)
        {
            log.Info($"[DEBUG] ClientId: {clientId}");
            bool useCached = false;
            string cachedTokenValue = null;
            lock (_tokenCacheLock)
            {
                if (_tokenCache.TryGetValue(clientId, out var cachedToken))
                {
                    if (DateTime.Now < cachedToken.ExpirationTime)
                    {
                        log.Info($"[DEBUG] Tenant: {tenant.Name} - Using cached token");
                        useCached = true;
                        cachedTokenValue = cachedToken.Token;
                    }
                    else
                    {
                        log.Info($"[DEBUG] Tenant: {tenant.Name} - Token expired, getting a new one");
                    }
                }
            }
            if (useCached)
            {
                return cachedTokenValue;
            }
            using (var client = new HttpClient())
            {
                log.Info($"[DEBUG] Tenant: {tenant.Name} - Getting new token");
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                var content = new StringContent("grant_type=client_credentials", Encoding.UTF8,
                    "application/x-www-form-urlencoded");

                HttpResponseMessage response = await client.PostAsync(tokenUrl, content);
                if (!response.IsSuccessStatusCode)
                    throw new InvalidCallToHarriException(response.StatusCode);

                var jsonContent = await response.Content.ReadAsStringAsync();
                TokenResponse tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonContent);

                // insert new token into tokencache.  use a 120 second buffer
                var expirationTime = DateTime.Now.AddSeconds(tokenResponse.ExpiresIn - 120);
                lock (_tokenCacheLock)
                {
                    _tokenCache[clientId] = (tokenResponse.AccessToken, expirationTime);
                }
                log.Info($"[DEBUG] Tenant: {tenant.Name} -  ExpiresIn: {tokenResponse.ExpiresIn}, ExpirationTime: {expirationTime:T}");

                return tokenResponse.AccessToken;
            }
        }
    }
}
