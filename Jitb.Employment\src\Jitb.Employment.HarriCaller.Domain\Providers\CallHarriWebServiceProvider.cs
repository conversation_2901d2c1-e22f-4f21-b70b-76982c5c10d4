﻿using Jitb.Employment.Domain.Concepts.Config;
using Jitb.Employment.Domain.Dictionaries;
using Jitb.Employment.HarriCaller.Domain;
using Jitb.Employment.HarriCaller.Domain.Exceptions;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using Newtonsoft.Json;
using NLog;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace Jitb.Employment.HarriCaller.Domain.Providers
{
    public interface ICallHarriWebServiceProvider
    {
        Task<RestResponse> Call(Guid tenantId, Method callType, string endpoint, string payload, bool failIfNotFound, string version);
        Task<RestResponse> Call(HarriTenant tenant, Method callType, string endpoint, string payload, bool failIfNotFound, string version);
    }

    public class CallHarriWebServiceProvider : ICallHarriWebServiceProvider
    {

        private readonly IHarriTenantTable _harriTenantTable;
        private readonly ILogger _log;
        private readonly IRateLimit _rateLimit;
        private const string BASEURL = @"https://gateway.harri.com/open-api-hub/";

        // Static dictionary to persist token cache across multiple calls
        private static readonly Dictionary<string, (string Token, DateTime ExpirationTime)> _tokenCache =
            new Dictionary<string, (string Token, DateTime ExpirationTime)>();

        public CallHarriWebServiceProvider(IHarriTenantTable harriTenantTable, ILogger log, IRateLimit rateLimit)
        {
            _harriTenantTable = harriTenantTable;
            _log = log;
            _rateLimit = rateLimit;
        }

        public async Task<RestResponse> Call(Guid tenantId, Method callType, string endpoint, string payload,
            bool failIfNotFound, string version)
        {
            _log.Info($@"
Calling Harri With TenantId: {tenantId}, Method: {callType.ToString()}, Endpoint: {endpoint}, Payload: {payload}
");
            var tenant = _harriTenantTable.Get(tenantId);


            return await Call(tenant, callType, endpoint, payload, failIfNotFound, version);
        }


        //Good Version
        public async Task<RestResponse> Call(HarriTenant tenant, Method callType, string endpoint, string payload,
            bool failIfNotFound, string version)
        {
            _log.Info($@"
Calling Harri with Tenant: {tenant.TenantId}, Method: {callType.ToString()}, Endpoint: {endpoint}, Payload: {payload}
");
            string tokenUrl = tenant.TokenUrl;
            string clientId = tenant.Client;
            string clientSecret = tenant.Secret;


            var token = GetOAuthTokenAsync(tokenUrl, clientId, clientSecret, _log, tenant).GetAwaiter().GetResult();

            _log.Info($"Token: {token}");

            var request = new RestRequest(endpoint, callType);
            return await CallHarri(tenant, token, request, payload, failIfNotFound, version);
        }


        private async Task<RestResponse> CallHarri(HarriTenant tenant, string pmToken, RestRequest request, string payload, bool failIfNotFound, string version)
        {
            // replacing baseurl with constant instead of baseurl from tenant table.
            string url = $@"{BASEURL}/api/{version.ToLower()}";

            // Create a rate limit key based on the tenant and endpoint
            string rateLimitKey = $"harri-api-{tenant.TenantId}-{request.Resource}";

            // Extra debug: log the rate limit key and time
            _log.Info($"[RateLimit] Using key: {rateLimitKey} at {DateTime.UtcNow:O}");            // Check if we can make the request based on rate limits
            // Implement a retry loop with exponential backoff for rate limiting
            var retryCount = 0;
            const int maxRetries = 3;
            var baseDelayMs = 1000;

            while (retryCount < maxRetries)
            {
                if (await _rateLimit.TryMakeRequestAsync(rateLimitKey))
                {
                    // Rate limit check passed, we can proceed with the API call
                    break;
                }

                retryCount++;
                var delayMs = baseDelayMs * (int)Math.Pow(2, retryCount - 1); // Exponential backoff
                
                _log.Warn($"Rate limit exceeded for tenant {tenant.TenantId} and endpoint {request.Resource}. " +
                         $"Retry {retryCount}/{maxRetries} - waiting {delayMs}ms before retry...");

                if (retryCount < maxRetries)
                {
                    await Task.Delay(delayMs);
                }
                else
                {
                    // After max retries, we'll proceed anyway but log a warning
                    // This ensures the application continues to function, albeit potentially exceeding rate limits
                    _log.Error($"Failed to respect rate limit for tenant {tenant.TenantId} and endpoint {request.Resource} " +
                              $"after {maxRetries} retries. Proceeding with API call anyway.");
                }
            }

            using (var client = new RestClient(url))
            {
                var token = $@"Bearer {pmToken}";
                request.AddHeader("Authorization", token);
                request.Timeout = 30000;
                if (payload != null) request.AddBody(payload);
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;

                RestResponse resp;

                try
                {
                    resp = await client.ExecuteAsync(request);
                    _log.Info($"Response Status: {resp.StatusCode}");
                }
                catch (Exception e)
                {
                    _log.Error(e);
                    throw;
                }

                if (resp.StatusCode != HttpStatusCode.OK && failIfNotFound)
                {
                    throw new InvalidCallToHarriException(resp, tenant, request, payload);
                }

                return resp;
            }
        }

        private static async Task<string> GetOAuthTokenAsync(string tokenUrl, string clientId, string clientSecret, ILogger log, HarriTenant tenant)
        {

            if (_tokenCache.TryGetValue(clientId, out var cachedToken))
            {

                if (DateTime.Now < cachedToken.ExpirationTime)
                {
                    log.Info($"Tenant: {tenant.Name} - Using cached token");
                    return cachedToken.Token;
                }
                log.Info($"Tenant: {tenant.Name} - Token expired, getting a new one");
            }

            using (var client = new HttpClient())
            {
                log.Info($"Tenant: {tenant.Name} - Getting new token");
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{clientId}:{clientSecret}"));
                client.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", credentials);

                var content = new StringContent("grant_type=client_credentials", Encoding.UTF8,
                    "application/x-www-form-urlencoded");

                HttpResponseMessage response = await client.PostAsync(tokenUrl, content);
                if (!response.IsSuccessStatusCode)
                    throw new InvalidCallToHarriException(response.StatusCode);

                var jsonContent = await response.Content.ReadAsStringAsync();
                TokenResponse tokenResponse = JsonConvert.DeserializeObject<TokenResponse>(jsonContent);

                // insert new token into tokencache.  use a 120 second buffer
                var expirationTime = DateTime.Now.AddSeconds(tokenResponse.ExpiresIn - 120);
                _tokenCache[clientId] = (tokenResponse.AccessToken, expirationTime);
                log.Info($"Tenant: {tenant.Name} -  ExpiresIn: {tokenResponse.ExpiresIn}, ExpirationTime: {expirationTime:T}");


                return tokenResponse.AccessToken;
            }
        }

    }
}