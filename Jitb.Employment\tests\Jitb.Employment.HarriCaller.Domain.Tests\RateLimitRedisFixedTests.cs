using System;
using System.Configuration;
using System.Threading.Tasks;
using FluentAssertions;
using Jitb.Employment.HarriCaller.Domain.RateLimiting;
using NLog;
using StackExchange.Redis;
using Xunit;
using Xunit.Abstractions;

namespace Jitb.Employment.HarriCaller.Domain.Tests
{
    public class RateLimitRedisFixedTests : IDisposable
    {
        private readonly ITestOutputHelper _output;
        private readonly RateLimitRedisFixed _rateLimiter;
        private readonly ILogger _logger;
        private readonly string _redisConnectionString;

        public RateLimitRedisFixedTests(ITestOutputHelper output)
        {
            _output = output;
            _logger = LogManager.GetCurrentClassLogger();
            _redisConnectionString = ConfigurationManager.ConnectionStrings["RedisConnection"]?.ConnectionString
                                      ?? ConfigurationManager.ConnectionStrings["Redis"]?.ConnectionString
                                      ?? "localhost:6379";
            _output.WriteLine($"Using Redis connection: {_redisConnectionString}");
            _rateLimiter = new RateLimitRedisFixed(_redisConnectionString, _logger, 3, "test-rate-limit-fixed:");
        }

        [Fact]
        public async Task TryMakeRequestAsync_WithinLimit_ShouldReturnTrue()
        {
            var key = $"test-{Guid.NewGuid()}";
            
            // First request should succeed
            var result = await _rateLimiter.TryMakeRequestAsync(key);
            result.Should().BeTrue("first request should be allowed");
        }

        [Fact]
        public async Task TryMakeRequestAsync_ExceedingLimit_ShouldReturnFalse()
        {
            var key = $"test-{Guid.NewGuid()}";
            
            // Make 3 requests (our limit)
            for (int i = 0; i < 3; i++)
            {
                var result = await _rateLimiter.TryMakeRequestAsync(key);
                result.Should().BeTrue($"request {i + 1} should be allowed");
            }
            
            // 4th request should be denied
            var deniedResult = await _rateLimiter.TryMakeRequestAsync(key);
            deniedResult.Should().BeFalse("request exceeding limit should be denied");
        }

        [Fact]
        public async Task TryMakeRequestAsync_RapidRequests_ShouldHandleCollisions()
        {
            var key = $"test-rapid-{Guid.NewGuid()}";
            
            // Make rapid requests to test for timestamp collisions
            var tasks = new Task<bool>[5];
            for (int i = 0; i < 5; i++)
            {
                tasks[i] = _rateLimiter.TryMakeRequestAsync(key);
            }
            
            var results = await Task.WhenAll(tasks);
            
            // With limit of 3, we should get 3 true and 2 false
            var allowedCount = 0;
            var deniedCount = 0;
            
            foreach (var result in results)
            {
                if (result)
                    allowedCount++;
                else
                    deniedCount++;
            }
            
            allowedCount.Should().Be(3, "exactly 3 requests should be allowed");
            deniedCount.Should().Be(2, "exactly 2 requests should be denied");
        }

        [Fact]
        public async Task CanMakeRequestAsync_WithinLimit_ShouldReturnTrue()
        {
            var key = $"test-can-{Guid.NewGuid()}";
            
            // Record a couple requests
            await _rateLimiter.RecordRequestAsync(key);
            await _rateLimiter.RecordRequestAsync(key);
            
            // Should still be able to make one more
            var canMake = await _rateLimiter.CanMakeRequestAsync(key);
            canMake.Should().BeTrue("should be able to make request within limit");
        }

        [Fact]
        public async Task CanMakeRequestAsync_AtLimit_ShouldReturnFalse()
        {
            var key = $"test-can-limit-{Guid.NewGuid()}";
            
            // Record 3 requests (our limit)
            for (int i = 0; i < 3; i++)
            {
                await _rateLimiter.RecordRequestAsync(key);
            }
            
            // Should not be able to make another
            var canMake = await _rateLimiter.CanMakeRequestAsync(key);
            canMake.Should().BeFalse("should not be able to make request at limit");
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldNotThrow()
        {
            var key = $"test-record-{Guid.NewGuid()}";
            
            // Recording should not throw
            await _rateLimiter.RecordRequestAsync(key);
            await _rateLimiter.RecordRequestAsync(key);
            await _rateLimiter.RecordRequestAsync(key);
            
            // This should complete without exceptions
            true.Should().BeTrue("record operations should complete successfully");
        }

        [Fact]
        public async Task DifferentKeys_ShouldHaveIndependentLimits()
        {
            var key1 = $"test-key1-{Guid.NewGuid()}";
            var key2 = $"test-key2-{Guid.NewGuid()}";
            
            // Exhaust limit for key1
            for (int i = 0; i < 3; i++)
            {
                await _rateLimiter.TryMakeRequestAsync(key1);
            }
            
            // key1 should be at limit
            var key1Result = await _rateLimiter.TryMakeRequestAsync(key1);
            key1Result.Should().BeFalse("key1 should be at limit");
            
            // key2 should still be available
            var key2Result = await _rateLimiter.TryMakeRequestAsync(key2);
            key2Result.Should().BeTrue("key2 should have independent limit");
        }

        [Fact]
        public async Task RecordRequestAsync_ShouldWriteToRedis()
        {
            var key = $"test-redis-verify-{Guid.NewGuid()}";
            var fullKey = $"test-rate-limit-fixed:{key}";

            // Record a request
            await _rateLimiter.RecordRequestAsync(key);

            // Connect directly to Redis and verify the key exists and has at least one entry
            using (var redis = await ConnectionMultiplexer.ConnectAsync(_redisConnectionString))
            {
                var db = redis.GetDatabase();
                var length = await db.SortedSetLengthAsync(fullKey);
                _output.WriteLine($"Redis sorted set '{fullKey}' length: {length}");
                length.Should().BeGreaterThan(0, $"Sorted set '{fullKey}' should have at least one entry after RecordRequestAsync");
            }
        }

        public void Dispose()
        {
            _rateLimiter?.Dispose();
        }
    }
}
